// React构建产物示例JavaScript文件
(function() {
    'use strict';
    
    // 模拟React应用
    class NumbatApp {
        constructor() {
            this.isLoggedIn = false;
            this.token = localStorage.getItem('token');
            this.init();
        }
        
        init() {
            if (this.token) {
                this.isLoggedIn = true;
                this.showDashboard();
            } else {
                this.showLogin();
            }
        }
        
        showLogin() {
            const root = document.getElementById('root');
            root.innerHTML = `
                <div class="login-container">
                    <form class="login-form" id="loginForm">
                        <h1>Numbat 传感器管理系统</h1>
                        <div class="form-group">
                            <label for="username">用户名</label>
                            <input type="text" id="username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="password">密码</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                        <button type="submit" class="login-btn">登录</button>
                        <div id="error" class="error" style="display: none;"></div>
                    </form>
                </div>
            `;
            
            document.getElementById('loginForm').addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }
        
        async handleLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('error');
            const submitBtn = document.querySelector('.login-btn');
            
            submitBtn.disabled = true;
            submitBtn.textContent = '登录中...';
            errorDiv.style.display = 'none';
            
            try {
                const response = await fetch('/api/v1/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password }),
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('token', data.data.token);
                    this.token = data.data.token;
                    this.isLoggedIn = true;
                    this.showDashboard();
                } else {
                    errorDiv.textContent = data.message || '登录失败';
                    errorDiv.style.display = 'block';
                }
            } catch (error) {
                errorDiv.textContent = '网络错误，请稍后重试';
                errorDiv.style.display = 'block';
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '登录';
            }
        }
        
        async showDashboard() {
            const root = document.getElementById('root');
            root.innerHTML = `
                <div class="App">
                    <header class="header">
                        <div class="header-content">
                            <h1>Numbat 传感器管理系统</h1>
                            <button class="logout-btn" onclick="app.logout()">退出登录</button>
                        </div>
                    </header>
                    <main class="main-content">
                        <h2>传感器列表</h2>
                        <div id="sensorsContainer">
                            <p>加载中...</p>
                        </div>
                    </main>
                </div>
            `;
            
            this.loadSensors();
        }
        
        async loadSensors() {
            try {
                const response = await fetch('/api/v1/sensors', {
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                    },
                });
                
                if (response.ok) {
                    const data = await response.json();
                    this.renderSensors(data.data || []);
                } else {
                    document.getElementById('sensorsContainer').innerHTML = 
                        '<p>获取传感器数据失败</p>';
                }
            } catch (error) {
                document.getElementById('sensorsContainer').innerHTML = 
                    '<p>网络错误，请稍后重试</p>';
            }
        }
        
        renderSensors(sensors) {
            const container = document.getElementById('sensorsContainer');
            
            if (sensors.length === 0) {
                container.innerHTML = '<p>暂无传感器数据</p>';
                return;
            }
            
            const sensorsHtml = sensors.map(sensor => `
                <div class="sensor-card">
                    <h3>设备 ${sensor.id}</h3>
                    <p><strong>状态:</strong> ${sensor.status}</p>
                    <p><strong>主传感器:</strong> ${sensor.primary_sensor_type}</p>
                    <p><strong>从传感器:</strong> ${sensor.secondary_sensor_type}</p>
                    <p><strong>最后心跳:</strong> ${new Date(sensor.last_heartbeat_ms).toLocaleString()}</p>
                </div>
            `).join('');
            
            container.innerHTML = `<div class="sensors-grid">${sensorsHtml}</div>`;
        }
        
        logout() {
            localStorage.removeItem('token');
            this.token = null;
            this.isLoggedIn = false;
            this.showLogin();
        }
    }
    
    // 启动应用
    window.app = new NumbatApp();
    
    // 添加一些全局样式
    const style = document.createElement('style');
    style.textContent = `
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .sensor-status-online {
            color: #27ae60;
        }
        
        .sensor-status-offline {
            color: #e74c3c;
        }
    `;
    document.head.appendChild(style);
    
})(); 